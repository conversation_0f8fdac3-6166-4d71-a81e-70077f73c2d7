<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Canvider AI Score | Score Your Candidates with AI</title>
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Modern Design System CSS -->
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="bg-animation"></div>

    <header class="header">
      <nav class="navbar nav-container">
        <div class="logo">
          <a href="./index.html">Canvider</a>
        </div>
        <div class="menu-toggle" onclick="toggleMenu()">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="nav-content">
          <ul class="nav-links">
            <li><a href="./index.html#features">Features</a></li>
            <li><a href="./index.html#pricing">Pricing</a></li>
            <li><a href="./index.html#resources">Resources</a></li>
            <li><a href="./index.html#contact">Contact</a></li>
          </ul>
          <div class="nav-buttons">
            <a href="#login" class="btn btn-secondary">Login</a>
            <a href="#demo" class="btn btn-primary">Request Demo</a>
          </div>
        </div>
      </nav>
    </header>

    <script>
      function toggleMenu() {
        document.querySelector('.nav-content').classList.toggle('active');
      }
      
      window.addEventListener('scroll', () => {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }
      });
    </script>

    <!-- Hero Section -->
    <section class="hero" style="min-height: 80vh;">
      <div class="hero-content">
        <div style="display: inline-block; background: var(--primary-gradient); padding: 0.5rem 1rem; border-radius: 50px; color: white; font-size: 0.9rem; font-weight: 600; margin-bottom: 1.5rem;">
          🎯 AI-Powered Candidate Scoring
        </div>
        <h1 style="font-size: clamp(2.5rem, 6vw, 4rem);">Canvider AI Score</h1>
        <p style="font-size: 1.2rem; max-width: 600px; margin: 0 auto 2rem;">
          Score Your Candidates with AI. When there are many candidates applying
          for a position, Canvider AI Score uses AI to evaluate each candidate's
          resume and score them based on how well they match the job description.
        </p>
        <div class="hero-buttons">
          <a href="#demo" class="btn btn-primary btn-hero">Start Today</a>
          <a href="#how-it-works" class="btn btn-secondary btn-hero">See How It Works</a>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" style="padding: 6rem 5%; background: rgba(10, 10, 10, 0.5);">
      <div class="container row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 4rem; align-items: center;">
        <!-- Steps Content -->
        <div class="col-12">
          <h2 style="color: var(--text-primary); margin-bottom: 3rem; font-size: 2.5rem;">How it Works</h2>
          
          <div style="display: flex; flex-direction: column; gap: 2rem;">
            <!-- Step 1 -->
            <div class="ht-step">
              <div class="ht-step-nr">1</div>
              <div>
                <h3 class="ht-step-title">Go to Applicant's Profile</h3>
                <p class="ht-step-p">Navigate to any applicant profile in your dashboard</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="ht-step">
              <div class="ht-step-nr">2</div>
              <div>
                <h3 class="ht-step-title">Click "Analyze with AI"</h3>
                <p class="ht-step-p">Let our AI analyze the candidate's profile against your requirements</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="ht-step">
              <div class="ht-step-nr">3</div>
              <div>
                <h3 class="ht-step-title">Review Score & Highlights</h3>
                <p class="ht-step-p">Get instant insights about candidate's match score and key strengths</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Demo -->
        <div class="ht-video col-12">
          <iframe 
            class="ht-video-frame"
            src="https://www.youtube.com/embed/tCDvOQI3pco?autoplay=1&controls=0&showinfo=0&rel=0&modestbranding=1&mute=1&loop=1&playlist=tCDvOQI3pco" 
            title="Product Demo" 
            frameborder="0"
            allow="autoplay"
            style="width: 100%;">
          </iframe>
        </div>
      </div>
    </section>

    <!-- Key Benefits Section -->
    <section id="features" style="padding: 6rem 5%;">
      <div class="container">
        <div class="section-title">
          <h2 style="color: var(--text-primary); margin-bottom: 1rem;">Why use Canvider AI Score?</h2>
          <p style="color: var(--text-muted); max-width: 600px; margin: 0 auto 4rem;">
            It can help you save time and resources by automating the initial screening process, allowing you to focus on the most promising candidates.
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem;">
          <!-- Benefit 1 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
              ⏱️
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">Save hours of Screening Time</h3>
            <p style="color: var(--text-muted); line-height: 1.6; margin-bottom: 1.5rem;">
              Stop spending days reading through resumes. Our AI does the heavy lifting, allowing you to focus on interviewing the best candidates.
            </p>
          </div>

          <!-- Benefit 2 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
              🎯
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">Identify Top Candidates</h3>
            <p style="color: var(--text-muted); line-height: 1.6; margin-bottom: 1.5rem;">
              Get ranked candidate lists with detailed match scores. Start with the highest-scoring candidates and work your way down.
            </p>
          </div>

          <!-- Benefit 3 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
              💡
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">AI-Generated Insights</h3>
            <p style="color: var(--text-muted); line-height: 1.6; margin-bottom: 1.5rem;">
              Get detailed highlights for each candidate. Three positive strengths and three observed weaknesses to make informed decisions.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section style="padding: 6rem 5%; background: var(--surface-dark); text-align: center;">
      <div class="container" style="max-width: 800px;">
        <h2 style="color: var(--text-primary); font-size: 2.5rem; margin-bottom: 1rem;">
          Ready to Score Your Candidates with AI?
        </h2>
        <p style="color: var(--text-muted); font-size: 1.2rem; margin-bottom: 3rem; line-height: 1.6;">
          Join hundreds of companies using Canvider AI Score to streamline their hiring process.
          Start with the free tier today and see the difference AI can make.
        </p>

        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 3rem;">
          <a href="#demo" class="btn btn-primary btn-hero">Start Today</a>
          <a href="#contact" class="btn btn-secondary btn-hero">Ask your Questions</a>
        </div>

      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-brand">
            <div class="footer-logo">
              <div class="footer-logo-icon">C</div>
              <div class="footer-logo-text">Canvider</div>
            </div>
            <p class="footer-description">
              Streamline your hiring with AI-powered candidate scoring and intelligent recruitment tools.
            </p>
            <div class="footer-social">
              <a href="#" class="footer-social-link">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="#" class="footer-social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="footer-social-link">
                <i class="fab fa-facebook"></i>
              </a>
            </div>
          </div>

          <div class="footer-section">
            <h4>Product</h4>
            <ul class="footer-links">
              <li><a href="black_modern.html#features">Features</a></li>
              <li><a href="black_modern.html#pricing">Pricing</a></li>
              <li><a href="canv_ai_score.html">AI Score</a></li>
              <li><a href="talentpool_feature.html">TalentPool</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4>Company</h4>
            <ul class="footer-links">
              <li><a href="#about">About Us</a></li>
              <li><a href="black_modern.html#contact">Contact</a></li>
              <li><a href="#careers">Careers</a></li>
              <li><a href="#blog">Blog</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4>Support</h4>
            <ul class="footer-links">
              <li><a href="#help">Help Center</a></li>
              <li><a href="#docs">Documentation</a></li>
              <li><a href="#api">API</a></li>
              <li><a href="#status">Status</a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Canvider. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- JavaScript for smooth animations -->
    <script>
      // Header background on scroll
      let header = null;
      function updateHeader() {
        if (!header) header = document.querySelector("header");

        if (window.scrollY > 100) {
          header.style.background = "rgba(10, 10, 10, 0.98)";
          header.style.backdropFilter = "blur(25px)";
          header.style.boxShadow = "0 4px 30px rgba(102, 126, 234, 0.1)";
        } else {
          header.style.background = "rgba(10, 10, 10, 0.95)";
          header.style.backdropFilter = "blur(20px)";
          header.style.boxShadow = "none";
        }
      }

      // Throttled scroll event
      let ticking = false;
      function requestUpdate() {
        if (!ticking) {
          requestAnimationFrame(() => {
            updateHeader();
            ticking = false;
          });
          ticking = true;
        }
      }

      window.addEventListener("scroll", requestUpdate, { passive: true });

      // Initial setup
      updateHeader();
    </script>
  </body>
</html>
