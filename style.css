/* Canvider Modern Design System */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea, #764ba2);
    --primary-blue: #667eea;
    --primary-purple: #764ba2;
    --background-dark: #0a0a0a;
    --surface-dark: rgba(20, 20, 20, 0.8);
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #b0b0b0;
    --border-subtle: rgba(255, 255, 255, 0.1);
    --shadow-primary: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    --shadow-glow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: var(--background-dark);
    overflow-x: hidden;
}

/* Layout Utilities */
.container {
    max-width: 1400px;
    margin: 0 auto;
}

.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}

.limited-width-content-xl {
    max-width: 1400px !important;
    margin: auto !important;
}

/* Spacing Utilities */
.space-from-top {
    margin-top: 10vw !important;
}

.space-from-top-m {
    margin-top: 6vw !important;
}

.space-from-bottom {
    margin-bottom: 6vh !important;
}

.space-from-bottom-xl {
    margin-bottom: 10vw !important;
}

.stretch-text {
    text-align: justify;
    letter-spacing: 3px;
}

/* Background Animations */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--primary-gradient);
    opacity: 0.1;
}

.bg-animation::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

/* Section Divider */
.section-divider {
    height: 1px;
    background: var(--primary-gradient);
    margin: 0 auto;
    width: 80%;
    opacity: 0.6;
    position: relative;
}

.section-divider::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }
}

/* Geometric Grid Background */
.geometric-grid {
    position: absolute;
    top: -50%;
    left: 0;
    width: 100%;
    height: 150%;
    overflow: hidden;
    z-index: -2;
}

/* Grid Lines and Animations */
.grid-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.4), rgba(118, 75, 162, 0.4), transparent);
    animation: gridMove 15s infinite linear;
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
    left: 0;
}

.grid-line.vertical {
    width: 1px;
    height: 100%;
    top: 0;
}

/* Grid line positioning */
.grid-line:nth-child(1) {
    top: 10%;
    animation-delay: 0s;
}

.grid-line:nth-child(2) {
    top: 25%;
    animation-delay: -3s;
}

.grid-line:nth-child(3) {
    top: 40%;
    animation-delay: -6s;
}

.grid-line:nth-child(4) {
    top: 55%;
    animation-delay: -9s;
}

.grid-line:nth-child(5) {
    top: 70%;
    animation-delay: -12s;
}

.grid-line:nth-child(6) {
    top: 85%;
    animation-delay: -15s;
}

.grid-line:nth-child(7) {
    left: 10%;
    animation-delay: -1s;
}

.grid-line:nth-child(8) {
    left: 25%;
    animation-delay: -4s;
}

.grid-line:nth-child(9) {
    left: 40%;
    animation-delay: -7s;
}

.grid-line:nth-child(10) {
    left: 55%;
    animation-delay: -10s;
}

.grid-line:nth-child(11) {
    left: 70%;
    animation-delay: -13s;
}

.grid-line:nth-child(12) {
    left: 85%;
    animation-delay: -16s;
}

@keyframes gridMove {
    0% {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(2px);
    }

    20% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0px);
    }

    80% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0px);
    }

    100% {
        opacity: 0;
        transform: scale(1.2);
        filter: blur(2px);
    }
}

/* Grid Intersection Points */
.grid-intersection {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    animation: intersectionPulse 8s infinite ease-in-out;
}

.grid-intersection:nth-child(13) {
    top: 25%;
    left: 25%;
    animation-delay: 0s;
}

.grid-intersection:nth-child(14) {
    top: 40%;
    left: 70%;
    animation-delay: -2s;
}

.grid-intersection:nth-child(15) {
    top: 70%;
    left: 40%;
    animation-delay: -4s;
}

.grid-intersection:nth-child(16) {
    top: 55%;
    left: 85%;
    animation-delay: -6s;
}

@keyframes intersectionPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }

    50% {
        transform: scale(1.5);
        opacity: 1;
        box-shadow: 0 0 40px rgba(102, 126, 234, 1);
    }
}

/* Header */
header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-subtle);
    z-index: 1000;
    transition: all 0.3s ease;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Logo and Navigation */
.logo {
    font-size: 1.8rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-blue);
}

.nav-links a::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-subtle);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn-hero {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8rem 5% 4rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 800px;
    animation: fadeInUp 1s ease-out;
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
}

.hero p {
    font-size: 1.3rem;
    color: var(--text-muted);
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Features Section */
.features {
    padding: 8rem 5%;
    backdrop-filter: blur(10px);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.2rem;
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.feature-card {
    background: var(--surface-dark);
    border: 1px solid var(--border-subtle);
    border-radius: 20px;
    padding: 2.5rem;
    transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    will-change: transform;
}

.feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

/* Feature Card Elements */
.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.feature-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
    transition: color 0.3s ease;
}

.feature-card:hover h3 {
    color: var(--primary-blue);
}

.feature-card p {
    color: var(--text-muted);
    line-height: 1.6;
    position: relative;
    z-index: 1;
    transition: color 0.3s ease;
    margin-bottom: 1rem;
}

.feature-card:hover p {
    color: var(--text-secondary);
}

/* Feature Additional Content */
.feature-additional {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: opacity 0.3s ease, max-height 0.3s ease;
    position: relative;
    z-index: 1;
}

.feature-card:hover .feature-additional {
    opacity: 1;
    max-height: 200px;
    margin-top: 1rem;
}

/* Feature Additional Elements */
.feature-additional h4 {
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-additional ul {
    list-style: none;
    padding: 0;
}

.feature-additional li {
    color: #cbd5e1;
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.2rem;
    font-size: 0.9rem;
}

.feature-additional li::before {
    content: "•";
    color: var(--primary-blue);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.feature-cta {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    margin-top: 1rem;
}

.feature-card:hover .feature-cta {
    opacity: 1;
    transform: translateY(0);
}

.feature-cta a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.feature-cta a:hover {
    color: var(--text-primary);
    transform: translateX(5px);
}

.feature-cta a::after {
    content: "→";
    transition: transform 0.3s ease;
}

.feature-cta a:hover::after {
    transform: translateX(3px);
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Pricing Section */
.pricing {
    padding: 8rem 5%;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-card {
    background: var(--surface-dark);
    border: 1px solid var(--border-subtle);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-card.featured {
    border-color: var(--primary-blue);
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.price {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.price-period {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.features-list li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.features-list li::before {
    content: "✓";
    color: var(--primary-blue);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.features-list .not-available::before {
    content: "X";
    color: red;
}

/* Footer */
.footer {
    background: #0f172a;
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 5%;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.footer-logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-links a,
.footer-links button {
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: none;
    border: none;
    cursor: pointer;
    text-align: left;
    font-family: inherit;
}

.footer-links a:hover,
.footer-links button:hover {
    color: var(--primary-blue);
    transform: translateX(4px);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    /* Disable hover effects on mobile */
    .feature-card:hover {
        transform: none;
        box-shadow: none;
    }

    .feature-card:hover .feature-icon {
        transform: none;
        box-shadow: none;
    }

    .feature-card:hover h3,
    .feature-card:hover p {
        transform: none;
        text-shadow: none;
    }

    .feature-additional {
        opacity: 1;
        max-height: none;
        margin-top: 1rem;
    }

    .feature-cta {
        opacity: 1;
        transform: none;
    }
}

/* Legacy Support Classes */
.pop-red-text {
    color: var(--primary-blue) !important;
}

.dark-red-text {
    color: var(--primary-purple) !important;
}

.demo-img {
    max-height: 300px;
}

.client-logo {
    max-height: 80px !important;
    max-width: 120px !important;
    width: 100%;
}

.bootstrap-icon {
    font-size: 100px !important;
    color: #050505 !important;
    animation-duration: 10s;
    animation-name: icon-animation;
    animation-iteration-count: infinite;
}

@keyframes icon-animation {
    0%, 100% {
        transform: scale(1) translateX(0);
    }

    50% {
        transform: scale(1.2) translateX(0);
    }
}

.bootstrap-icon-stable {
    font-size: 100px !important;
    color: #050505 !important;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}



* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: #0a0a0a;
    overflow-x: hidden;
}

.access-title::before {
    content: "";
    display: block;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
    width: 100%;
}

/* Section Divider */
.section-divider {
    height: 1px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    margin: 0 auto;
    width: 80%;
    opacity: 0.6;
    position: relative;
}

.section-divider::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(102, 126, 234, 0.8),
            rgba(118, 75, 162, 0.8),
            transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {

    0%,
    100% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }
}

/* Animated Background */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.1;
}

.bg-animation::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

/* Geometric Grid Background */
.geometric-grid {
    position: absolute;
    top: -50%;
    left: 0;
    width: 100%;
    height: 150%;
    overflow: hidden;
    z-index: -2;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg,
            transparent,
            rgba(102, 126, 234, 0.4),
            rgba(118, 75, 162, 0.4),
            transparent);
    animation: gridMove 15s infinite linear;
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
    left: 0;
}

.grid-line.vertical {
    width: 1px;
    height: 100%;
    top: 0;
}

/* Horizontal lines */
.grid-line:nth-child(1) {
    top: 10%;
    animation-delay: 0s;
}

.grid-line:nth-child(2) {
    top: 25%;
    animation-delay: -3s;
}

.grid-line:nth-child(3) {
    top: 40%;
    animation-delay: -6s;
}

.grid-line:nth-child(4) {
    top: 55%;
    animation-delay: -9s;
}

.grid-line:nth-child(5) {
    top: 70%;
    animation-delay: -12s;
}

.grid-line:nth-child(6) {
    top: 85%;
    animation-delay: -15s;
}

/* Vertical lines */
.grid-line:nth-child(7) {
    left: 10%;
    animation-delay: -1s;
}

.grid-line:nth-child(8) {
    left: 25%;
    animation-delay: -4s;
}

.grid-line:nth-child(9) {
    left: 40%;
    animation-delay: -7s;
}

.grid-line:nth-child(10) {
    left: 55%;
    animation-delay: -10s;
}

.grid-line:nth-child(11) {
    left: 70%;
    animation-delay: -13s;
}

.grid-line:nth-child(12) {
    left: 85%;
    animation-delay: -16s;
}

@keyframes gridMove {
    0% {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(2px);
    }

    20% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0px);
    }

    80% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0px);
    }

    100% {
        opacity: 0;
        transform: scale(1.2);
        filter: blur(2px);
    }
}

/* Grid Intersection Points */
.grid-intersection {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    animation: intersectionPulse 8s infinite ease-in-out;
}

.grid-intersection:nth-child(13) {
    top: 25%;
    left: 25%;
    animation-delay: 0s;
}

.grid-intersection:nth-child(14) {
    top: 40%;
    left: 70%;
    animation-delay: -2s;
}

.grid-intersection:nth-child(15) {
    top: 70%;
    left: 40%;
    animation-delay: -4s;
}

.grid-intersection:nth-child(16) {
    top: 55%;
    left: 85%;
    animation-delay: -6s;
}

@keyframes intersectionPulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.6;
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }

    50% {
        transform: scale(1.5);
        opacity: 1;
        box-shadow: 0 0 40px rgba(102, 126, 234, 1);
    }
}

/* Header */
header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.access-title {
    font-size: 1.2rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: #e0e0e0;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: #667eea;
}

.nav-links a::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #e0e0e0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8rem 5% 4rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 800px;
    animation: fadeInUp 1s ease-out;
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
}

.hero p {
    font-size: 1.3rem;
    color: #b0b0b0;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-hero {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Features Section */
.features {
    padding: 8rem 5%;
    backdrop-filter: blur(10px);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 3rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.2rem;
    color: #b0b0b0;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}



/* Benefits Section */
.benefits-section {
    padding: 5rem 5%;
    backdrop-filter: blur(10px);
}

.benefits-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.benefits-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease;
}

.benefit-item.visible {
    opacity: 1;
    transform: translateX(0);
}

.benefit-check {
    width: 32px;
    height: 32px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.benefit-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.benefit-text p {
    color: #b0b0b0;
    line-height: 1.6;
    font-size: 1rem;
}

.benefits-image {
    position: relative;
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.benefits-image.visible {
    opacity: 1;
    transform: translateX(0);
}

.image-container {
    position: relative;
}

.image-container img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.decoration-1 {
    position: absolute;
    bottom: -1.5rem;
    left: -1.5rem;
    width: 6rem;
    height: 6rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 1rem;
    opacity: 0.2;
}

.decoration-2 {
    position: absolute;
    top: -1.5rem;
    right: -1.5rem;
    width: 8rem;
    height: 8rem;
    background: #764ba2;
    border-radius: 1rem;
    opacity: 0.1;
}

.pricing {
    padding: 8rem 5%;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-card {
    background: rgba(20, 20, 20, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-description {
    color: #ffffff;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1.25rem;
    text-align: left;
}

.pricing-card.featured {
    border-color: #667eea;
    transform: scale(1.05);
    background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.1),
            rgba(118, 75, 162, 0.1));
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.price {
    font-size: 3rem;
    font-weight: 900;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.price-period {
    color: #b0b0b0;
    margin-bottom: 2rem;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.features-list li {
    color: #e0e0e0;
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.features-list li::before {
    content: "✓";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Enhanced Footer Styles */
.footer {
    background: #0f172a;
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    opacity: 0.6;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 5%;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand {
    display: flex;
    flex-direction: column;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.footer-logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.footer-logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: #94a3b8;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social-link {
    width: 44px;
    height: 44px;
    background: rgba(51, 65, 85, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-social-link:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.footer-social-link i {
    font-size: 1.2rem;
}

.footer-section h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
}

.footer-section h4::after {
    content: "";
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 2rem;
    height: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 1px;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-links li {
    transition: all 0.3s ease;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.25rem 0;
}

.footer-links a:hover {
    color: #667eea;
    transform: translateX(4px);
}

.footer-links button {
    background: none;
    border: none;
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.25rem 0;
    cursor: pointer;
    text-align: left;
    font-family: inherit;
}

.footer-links button:hover {
    color: #667eea;
    transform: translateX(4px);
}

.footer-bottom {
    border-top: 1px solid rgba(51, 65, 85, 0.8);
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: #64748b;
    font-size: 0.9rem;
}

/* Testimonials Section */
.testimonials {
    padding: 8rem 5%;
    backdrop-filter: blur(10px);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.testimonial-card {
    background: rgba(20, 20, 20, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05),
            rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.testimonial-card:hover::before {
    opacity: 1;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.star {
    color: #f59e0b;
    font-size: 1.25rem;
}

.testimonial-card blockquote {
    color: #e0e0e0;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-style: italic;
    quotes: none;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(102, 126, 234, 0.3);
}

.author-info {
    flex: 1;
}

.author-name {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.author-role {
    font-size: 0.875rem;
    color: #b0b0b0;
}

/* Contact Section */
.contact-section {
    padding: 8rem 5%;
    position: relative;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

/* Contact Info Column */
.contact-info h2 {
    font-size: clamp(2.5rem, 4vw, 3rem);
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.contact-info>p {
    font-size: 1.25rem;
    color: #cbd5e1;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.contact-items {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease;
}

.contact-item.visible {
    opacity: 1;
    transform: translateX(0);
}

.contact-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon svg {
    width: 24px;
    height: 24px;
    color: white;
}

.contact-details {
    flex: 1;
}

.contact-title {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.contact-value {
    color: #cbd5e1;
    line-height: 1.4;
}

/* Contact Form */
.contact-form-wrapper {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.contact-form-wrapper.visible {
    opacity: 1;
    transform: translateX(0);
}

.contact-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.contact-card h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 2rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    color: #1e293b;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #94a3b8;
}

.form-group select {
    cursor: pointer;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.error-message {
    font-size: 0.75rem;
    color: #dc2626;
    margin-top: 0.25rem;
    display: none;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #dc2626;
}

.form-group.error .error-message {
    display: block;
}

.btn-submit {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    font-size: 1.125rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-submit:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.btn-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.success-message {
    padding: 1rem;
    background: #10b981;
    color: white;
    border-radius: 8px;
    text-align: center;
    display: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .contact-info h2 {
        font-size: 2rem;
    }

    .contact-card {
        padding: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-brand {
        align-items: center;
    }

    .footer-social {
        justify-content: center;
    }

    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .benefits-content h2 {
        font-size: 2rem;
    }

    /* Disable hover effects on mobile */
    .feature-card:hover {
        transform: none;
        box-shadow: none;
    }

    .feature-card:hover .feature-icon {
        transform: none;
        box-shadow: none;
    }

    .feature-card:hover h3,
    .feature-card:hover p {
        transform: none;
        text-shadow: none;
    }

    .feature-additional {
        opacity: 1;
        max-height: none;
        margin-top: 1rem;
    }

    .feature-cta {
        opacity: 1;
        transform: none;
    }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}


.header {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: padding 0.3s ease;
}

.header.scrolled .nav-container {
    padding: 0.5rem 2rem;
}

.nav-content {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 6px;
    cursor: pointer;
}

.menu-toggle span {
    width: 25px;
    height: 2px;
    background-color: white;
    transition: 0.3s;
}

@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .nav-content {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.95);
        flex-direction: column;
        padding: 1rem;
        display: none;
    }

    .nav-content.active {
        display: flex;
    }

    .nav-links {
        flex-direction: column;
        align-items: center;
    }

    .nav-buttons {
        flex-direction: column;
        gap: 1rem;
    }
}

.ht-step {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.ht-video {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.ht-video-frame {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 20px;
}

.ht-step-nr {
    min-width: 50px !important;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.ht-step-title {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.ht-step-p {
    color: var(--text-muted);
    line-height: 1.4;
}